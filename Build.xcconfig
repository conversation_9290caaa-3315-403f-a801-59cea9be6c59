//
//  Build.xcconfig
//  MarkEditMac
//
//  Created by cyan on 12/26/22.
//
//  https://developer.apple.com/documentation/xcode/adding-a-build-configuration-file-to-your-project

CODE_SIGN_IDENTITY = -
DEVELOPMENT_TEAM =
PRODUCT_BUNDLE_IDENTIFIER = app.cyan.markedit-dev
SWIFT_ACTIVE_COMPILATION_CONDITIONS[sdk=macosx26.*] = $(inherited) BUILD_WITH_SDK_26_OR_LATER

MARKETING_VERSION = 1.25.0
CURRENT_PROJECT_VERSION = 75

#include? "Local.xcconfig"
