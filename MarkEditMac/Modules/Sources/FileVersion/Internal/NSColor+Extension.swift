//
//  NSColor+Extension.swift
//
//  Created by cyan on 10/17/24.
//

import AppKit

extension NSColor {
  static let addedText: NSColor = .theme(lightHexCode: 0x007757, darkHexCode: 0x3fb950)
  static let addedBackground: NSColor = .theme(lightHexCode: 0xe8fcf3, darkHexCode: 0x14261f)
  static let removedText: NSColor = .theme(lightHexCode: 0xc71f24, darkHexCode: 0xf85149)
  static let removedBackground: NSColor = .theme(lightHexCode: 0xffebeb, darkHexCode: 0x311b1f)
}
