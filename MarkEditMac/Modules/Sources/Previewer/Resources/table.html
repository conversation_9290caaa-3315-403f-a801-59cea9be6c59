<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>table</title>
  <style>
    :root {
      color-scheme: light dark;
    }

    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont;
      margin: 0px !important;
    }

    div {
      padding: 10px !important;
    }

    table {
      overflow: auto;
      margin-left: auto;
      margin-right: auto;
      border-spacing: 0;
      border-collapse: collapse;
    }

    table th {
      font-weight: 600;
      background-color: #00000005;
    }

    table th, table td {
      padding: 6px 13px;
      border: 1px solid #ccc;
    }

    @media (prefers-color-scheme: dark) {
      table th {
        background-color: #ffffff0a;
      }

      table th, table td {
        border: 1px solid #777;
      }
    }
  </style>
</head>
<body>
  <div id="container" style="width: 1600px; text-align: center; opacity: 0; transition: opacity 0.3s;"></div>
  <script src="https://cdn.jsdelivr.net/npm/marked@15.0.12/marked.min.js"></script>
  <script>
    const container = document.querySelector("#container");
    container.innerHTML = marked.parse("{{DATA}}".code);
  </script>
</body>
</html>
