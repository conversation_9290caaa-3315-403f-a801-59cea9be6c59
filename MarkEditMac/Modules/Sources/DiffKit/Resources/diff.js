(function(){"use strict";function h(){}h.prototype={diff:function(e,r){var t,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},u=i.callback;typeof i=="function"&&(u=i,i={});var o=this;function a(v){return v=o.postProcess(v,i),u?(setTimeout(function(){u(v)},0),!0):v}e=this.castInput(e,i),r=this.castInput(r,i),e=this.removeEmpty(this.tokenize(e,i)),r=this.removeEmpty(this.tokenize(r,i));var f=r.length,s=e.length,l=1,c=f+s;i.maxEditLength!=null&&(c=Math.min(c,i.maxEditLength));var d=(t=i.timeout)!==null&&t!==void 0?t:1/0,g=Date.now()+d,m=[{oldPos:-1,lastComponent:void 0}],p=this.extractCommon(m[0],r,e,0,i);if(m[0].oldPos+1>=s&&p+1>=f)return a(F(o,m[0].lastComponent,r,e,o.useLongestToken));var L=-1/0,E=1/0;function $(){for(var v=Math.max(L,-l);v<=Math.min(E,l);v+=2){var C=void 0,D=m[v-1],z=m[v+1];D&&(m[v-1]=void 0);var k=!1;if(z){var P=z.oldPos-v;k=z&&0<=P&&P<f}var Q=D&&D.oldPos+1<s;if(!k&&!Q){m[v]=void 0;continue}if(!Q||k&&D.oldPos<z.oldPos?C=o.addToPath(z,!0,!1,0,i):C=o.addToPath(D,!1,!0,1,i),p=o.extractCommon(C,r,e,v,i),C.oldPos+1>=s&&p+1>=f)return a(F(o,C.lastComponent,r,e,o.useLongestToken));m[v]=C,C.oldPos+1>=s&&(E=Math.min(E,v-1)),p+1>=f&&(L=Math.max(L,v+1))}l++}if(u)(function v(){setTimeout(function(){if(l>c||Date.now()>g)return u();$()||v()},0)})();else for(;l<=c&&Date.now()<=g;){var q=$();if(q)return q}},addToPath:function(e,r,t,i,u){var o=e.lastComponent;return o&&!u.oneChangePerToken&&o.added===r&&o.removed===t?{oldPos:e.oldPos+i,lastComponent:{count:o.count+1,added:r,removed:t,previousComponent:o.previousComponent}}:{oldPos:e.oldPos+i,lastComponent:{count:1,added:r,removed:t,previousComponent:o}}},extractCommon:function(e,r,t,i,u){for(var o=r.length,a=t.length,f=e.oldPos,s=f-i,l=0;s+1<o&&f+1<a&&this.equals(t[f+1],r[s+1],u);)s++,f++,l++,u.oneChangePerToken&&(e.lastComponent={count:1,previousComponent:e.lastComponent,added:!1,removed:!1});return l&&!u.oneChangePerToken&&(e.lastComponent={count:l,previousComponent:e.lastComponent,added:!1,removed:!1}),e.oldPos=f,s},equals:function(e,r,t){return t.comparator?t.comparator(e,r):e===r||t.ignoreCase&&e.toLowerCase()===r.toLowerCase()},removeEmpty:function(e){for(var r=[],t=0;t<e.length;t++)e[t]&&r.push(e[t]);return r},castInput:function(e){return e},tokenize:function(e){return Array.from(e)},join:function(e){return e.join("")},postProcess:function(e){return e}};function F(n,e,r,t,i){for(var u=[],o;e;)u.push(e),o=e.previousComponent,delete e.previousComponent,e=o;u.reverse();for(var a=0,f=u.length,s=0,l=0;a<f;a++){var c=u[a];if(c.removed)c.value=n.join(t.slice(l,l+c.count)),l+=c.count;else{if(!c.added&&i){var d=r.slice(s,s+c.count);d=d.map(function(g,m){var p=t[l+m];return p.length>g.length?p:g}),c.value=n.join(d)}else c.value=n.join(r.slice(s,s+c.count));s+=c.count,c.added||(l+=c.count)}}return u}var U=new h;function X(n,e,r){return U.diff(n,e,r)}function V(n,e){var r;for(r=0;r<n.length&&r<e.length;r++)if(n[r]!=e[r])return n.slice(0,r);return n.slice(0,r)}function B(n,e){var r;if(!n||!e||n[n.length-1]!=e[e.length-1])return"";for(r=0;r<n.length&&r<e.length;r++)if(n[n.length-(r+1)]!=e[e.length-(r+1)])return n.slice(-r);return n.slice(-r)}function A(n,e,r){if(n.slice(0,e.length)!=e)throw Error("string ".concat(JSON.stringify(n)," doesn't start with prefix ").concat(JSON.stringify(e),"; this is a bug"));return r+n.slice(e.length)}function N(n,e,r){if(!e)return n+r;if(n.slice(-e.length)!=e)throw Error("string ".concat(JSON.stringify(n)," doesn't end with suffix ").concat(JSON.stringify(e),"; this is a bug"));return n.slice(0,-e.length)+r}function y(n,e){return A(n,e,"")}function x(n,e){return N(n,e,"")}function Z(n,e){return e.slice(0,Y(n,e))}function Y(n,e){var r=0;n.length>e.length&&(r=n.length-e.length);var t=e.length;n.length<e.length&&(t=n.length);var i=Array(t),u=0;i[0]=0;for(var o=1;o<t;o++){for(e[o]==e[u]?i[o]=i[u]:i[o]=u;u>0&&e[o]!=e[u];)u=i[u];e[o]==e[u]&&u++}u=0;for(var a=r;a<n.length;a++){for(;u>0&&n[a]!=e[u];)u=i[u];n[a]==e[u]&&u++}return u}var O="a-zA-Z0-9_\\u{C0}-\\u{FF}\\u{D8}-\\u{F6}\\u{F8}-\\u{2C6}\\u{2C8}-\\u{2D7}\\u{2DE}-\\u{2FF}\\u{1E00}-\\u{1EFF}",W=new RegExp("[".concat(O,"]+|\\s+|[^").concat(O,"]"),"ug"),w=new h;w.equals=function(n,e,r){return r.ignoreCase&&(n=n.toLowerCase(),e=e.toLowerCase()),n.trim()===e.trim()},w.tokenize=function(n){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r;if(e.intlSegmenter){if(e.intlSegmenter.resolvedOptions().granularity!="word")throw new Error('The segmenter passed must have a granularity of "word"');r=Array.from(e.intlSegmenter.segment(n),function(u){return u.segment})}else r=n.match(W)||[];var t=[],i=null;return r.forEach(function(u){/\s/.test(u)?i==null?t.push(u):t.push(t.pop()+u):/\s/.test(i)?t[t.length-1]==i?t.push(t.pop()+u):t.push(i+u):t.push(u),i=u}),t},w.join=function(n){return n.map(function(e,r){return r==0?e:e.replace(/^\s+/,"")}).join("")},w.postProcess=function(n,e){if(!n||e.oneChangePerToken)return n;var r=null,t=null,i=null;return n.forEach(function(u){u.added?t=u:u.removed?i=u:((t||i)&&G(r,i,t,u),r=u,t=null,i=null)}),(t||i)&&G(r,i,t,null),n};function _(n,e,r){return(r==null?void 0:r.ignoreWhitespace)!=null&&!r.ignoreWhitespace?j(n,e,r):w.diff(n,e,r)}function G(n,e,r,t){if(e&&r){var i=e.value.match(/^\s*/)[0],u=e.value.match(/\s*$/)[0],o=r.value.match(/^\s*/)[0],a=r.value.match(/\s*$/)[0];if(n){var f=V(i,o);n.value=N(n.value,o,f),e.value=y(e.value,f),r.value=y(r.value,f)}if(t){var s=B(u,a);t.value=A(t.value,a,s),e.value=x(e.value,s),r.value=x(r.value,s)}}else if(r)n&&(r.value=r.value.replace(/^\s*/,"")),t&&(t.value=t.value.replace(/^\s*/,""));else if(n&&t){var l=t.value.match(/^\s*/)[0],c=e.value.match(/^\s*/)[0],d=e.value.match(/\s*$/)[0],g=V(l,c);e.value=y(e.value,g);var m=B(y(l,g),d);e.value=x(e.value,m),t.value=A(t.value,l,m),n.value=N(n.value,l,l.slice(0,l.length-m.length))}else if(t){var p=t.value.match(/^\s*/)[0],L=e.value.match(/\s*$/)[0],E=Z(L,p);e.value=x(e.value,E)}else if(n){var $=n.value.match(/\s*$/)[0],q=e.value.match(/^\s*/)[0],v=Z($,q);e.value=y(e.value,v)}}var H=new h;H.tokenize=function(n){var e=new RegExp("(\\r?\\n)|[".concat(O,"]+|[^\\S\\n\\r]+|[^").concat(O,"]"),"ug");return n.match(e)||[]};function j(n,e,r){return H.diff(n,e,r)}var I=new h;I.tokenize=function(n,e){e.stripTrailingCr&&(n=n.replace(/\r\n/g,`
`));var r=[],t=n.split(/(\n|\r\n)/);t[t.length-1]||t.pop();for(var i=0;i<t.length;i++){var u=t[i];i%2&&!e.newlineIsToken?r[r.length-1]+=u:r.push(u)}return r},I.equals=function(n,e,r){return r.ignoreWhitespace?((!r.newlineIsToken||!n.includes(`
`))&&(n=n.trim()),(!r.newlineIsToken||!e.includes(`
`))&&(e=e.trim())):r.ignoreNewlineAtEof&&!r.newlineIsToken&&(n.endsWith(`
`)&&(n=n.slice(0,-1)),e.endsWith(`
`)&&(e=e.slice(0,-1))),h.prototype.equals.call(this,n,e,r)};function S(n,e,r){return I.diff(n,e,r)}var b=new h;b.tokenize=function(n){return n.split(/(\S.+?[.!?])(?=\s+|$)/)};var K=new h;K.tokenize=function(n){return n.split(/([{}:;,]|\s+)/)};function J(n){"@babel/helpers - typeof";return J=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},J(n)}var T=new h;T.useLongestToken=!0,T.tokenize=I.tokenize,T.castInput=function(n,e){var r=e.undefinedReplacement,t=e.stringifyReplacer,i=t===void 0?function(u,o){return typeof o>"u"?r:o}:t;return typeof n=="string"?n:JSON.stringify(R(n,null,null,i),i,"  ")},T.equals=function(n,e,r){return h.prototype.equals.call(T,n.replace(/,([\r\n])/g,"$1"),e.replace(/,([\r\n])/g,"$1"),r)};function R(n,e,r,t,i){e=e||[],r=r||[],t&&(n=t(i,n));var u;for(u=0;u<e.length;u+=1)if(e[u]===n)return r[u];var o;if(Object.prototype.toString.call(n)==="[object Array]"){for(e.push(n),o=new Array(n.length),r.push(o),u=0;u<n.length;u+=1)o[u]=R(n[u],e,r,t,i);return e.pop(),r.pop(),o}if(n&&n.toJSON&&(n=n.toJSON()),J(n)==="object"&&n!==null){e.push(n),o={},r.push(o);var a=[],f;for(f in n)Object.prototype.hasOwnProperty.call(n,f)&&a.push(f);for(a.sort(),u=0;u<a.length;u+=1)f=a[u],o[f]=R(n[f],e,r,t,f);e.pop(),r.pop()}else o=n;return o}var M=new h;M.tokenize=function(n){return n.slice()},M.join=M.removeEmpty=function(n){return n},window.diffLines=S,window.diffWords=_,window.diffChars=X})();
