{"sourceLanguage": "en", "strings": {"0C5-FE-fvI.title": {"comment": "Class = \"NSMenuItem\"; title = \"Float on Top\"; ObjectID = \"0C5-FE-fvI\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Float on Top"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "保持在顶部"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "保持在頂部"}}}}, "0DE-Av-fBj.title": {"comment": "Class = \"NSMenuItem\"; title = \"Version History\"; ObjectID = \"0DE-Av-fBj\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Version History"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "版本记录"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "版本紀錄"}}}}, "0NS-5A-glR.title": {"comment": "Class = \"NSMenuItem\"; title = \"New File from Clipboard\"; ObjectID = \"0NS-5A-glR\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "New File from Clipboard"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "从剪贴板新建"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "從剪貼簿新建"}}}}, "1b7-l0-nxx.title": {"comment": "Class = \"NSMenu\"; title = \"Find\"; ObjectID = \"1b7-l0-nxx\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Find"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "查找"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "尋找"}}}}, "1UK-8n-QPP.title": {"comment": "Class = \"NSMenuItem\"; title = \"Customize Toolbar…\"; ObjectID = \"1UK-8n-QPP\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Customize Toolbar…"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "自定工具栏…"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "自定工具列…"}}}}, "1Xt-HY-uBw.title": {"comment": "Class = \"NSMenuItem\"; title = \"MarkEdit\"; ObjectID = \"1Xt-HY-uBw\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "MarkE<PERSON>"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "MarkE<PERSON>"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "MarkE<PERSON>"}}}}, "2CK-gf-sE8.title": {"comment": "Class = \"NSMenuItem\"; title = \"Last Edited 3 Months Ago\"; ObjectID = \"2CK-gf-sE8\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Last Edited 3 Months Ago"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "上次编辑三个月前"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "上次編輯三個月前"}}}}, "2oI-Rn-ZJC.title": {"comment": "Class = \"NSMenuItem\"; title = \"Transformations\"; ObjectID = \"2oI-Rn-ZJC\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Transformations"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "转换"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "轉換"}}}}, "3IN-sU-3Bg.title": {"comment": "Class = \"NSMenu\"; title = \"Spelling\"; ObjectID = \"3IN-sU-3Bg\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Spelling"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "拼写"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "拼字"}}}}, "3rS-ZA-NoH.title": {"comment": "Class = \"NSMenu\"; title = \"Speech\"; ObjectID = \"3rS-ZA-NoH\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Speech"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "语音"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "語音"}}}}, "4EN-yA-p0u.title": {"comment": "Class = \"NSMenuItem\"; title = \"Find\"; ObjectID = \"4EN-yA-p0u\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Find"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "查找"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "尋找"}}}}, "4J7-dP-txa.title": {"comment": "Class = \"NSMenuItem\"; title = \"Enter Full Screen\"; ObjectID = \"4J7-dP-txa\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Enter Full Screen"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "进入全屏幕"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "進入全螢幕"}}}}, "4sb-4s-VLi.title": {"comment": "Class = \"NSMenuItem\"; title = \"Quit MarkEdit\"; ObjectID = \"4sb-4s-VLi\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Quit MarkEdit"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "退出 MarkEdit"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "結束 MarkEdit"}}}}, "5HO-Fv-Q1j.title": {"comment": "Class = \"NSMenu\"; title = \"Copy Path\"; ObjectID = \"5HO-Fv-Q1j\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Copy Path"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "拷贝路径"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "拷貝路徑"}}}}, "5kV-Vb-QxS.title": {"comment": "Class = \"NSMenuItem\"; title = \"About MarkEdit\"; ObjectID = \"5kV-Vb-QxS\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "About MarkEdit"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "关于 MarkEdit"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "關於 MarkEdit"}}}}, "5QF-Oa-p0T.title": {"comment": "Class = \"NSMenuItem\"; title = \"Edit\"; ObjectID = \"5QF-Oa-p0T\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Edit"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "编辑"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "編輯"}}}}, "5W0-LW-fUP.title": {"comment": "Class = \"NSMenuItem\"; title = \"Save All\"; ObjectID = \"5W0-LW-fUP\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Save All"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "全部存储"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "全部儲存"}}}}, "5Zc-N5-YRN.title": {"comment": "Class = \"NSMenuItem\"; title = \"Headers\"; ObjectID = \"5Zc-N5-YRN\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Headers"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "标题"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "標題"}}}}, "6dh-zS-Vam.title": {"comment": "Class = \"NSMenuItem\"; title = \"Redo\"; ObjectID = \"6dh-zS-Vam\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Redo"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "重做"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "重做"}}}}, "7eH-ja-yIc.title": {"comment": "Class = \"NSMenu\"; title = \"Line Endings\"; ObjectID = \"7eH-ja-yIc\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Line Endings"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "行尾格式"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "行尾格式"}}}}, "8CK-gi-aLX.title": {"comment": "Class = \"NSMenuItem\"; title = \"Quote\"; ObjectID = \"8CK-gi-aLX\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Quote"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "引用"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "引用"}}}}, "9ic-FL-obx.title": {"comment": "Class = \"NSMenuItem\"; title = \"Substitutions\"; ObjectID = \"9ic-FL-obx\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Substitutions"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "替换"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "替代"}}}}, "9un-8n-UQZ.title": {"comment": "Class = \"NSMenu\"; title = \"Commands\"; ObjectID = \"9un-8n-UQZ\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Commands"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "操作指令"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "操作指令"}}}}, "9yt-4B-nSM.title": {"comment": "Class = \"NSMenuItem\"; title = \"Smart Copy/Paste\"; ObjectID = \"9yt-4B-nSM\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Smart Copy/Paste"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "智能拷贝/粘贴"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "智慧型拷貝/貼上"}}}}, "53x-nK-wnX.title": {"comment": "Class = \"NSMenuItem\"; title = \"Copy Pandoc Command\"; ObjectID = \"53x-nK-wnX\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Copy Pandoc Command"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "拷贝 Pandoc 指令"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "拷貝 Pandoc 指令"}}}}, "78Y-hA-62v.title": {"comment": "Class = \"NSMenuItem\"; title = \"Correct Spelling Automatically\"; ObjectID = \"78Y-hA-62v\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Correct Spelling Automatically"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "自动纠正拼写"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "自動修正拼字"}}}}, "ANU-wC-fmd.title": {"comment": "Class = \"NSMenuItem\"; title = \"Classic Mac OS (CR)\"; ObjectID = \"ANU-wC-fmd\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Classic Mac OS (CR)"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "Classic Mac OS (CR)"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "Classic Mac OS (CR)"}}}}, "ANX-uP-CA4.title": {"comment": "Class = \"NSMenuItem\"; title = \"List\"; ObjectID = \"ANX-uP-CA4\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "List"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "列表"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "列表"}}}}, "arb-IC-voG.title": {"comment": "Class = \"NSMenuItem\"; title = \"Move Line Down\"; ObjectID = \"arb-IC-voG\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Move Line Down"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "向下移动行"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "向下移動行"}}}}, "aSl-kC-hp8.title": {"comment": "Class = \"NSMenuItem\"; title = \"Development Guide\"; ObjectID = \"aSl-kC-hp8\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Development Guide"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "开发指南"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "開發指南"}}}}, "aTl-1u-JFS.title": {"comment": "Class = \"NSMenuItem\"; title = \"Print…\"; ObjectID = \"aTl-1u-JFS\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Print…"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "打印…"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "列印…"}}}}, "aUF-d1-5bR.title": {"comment": "Class = \"NSMenuItem\"; title = \"Window\"; ObjectID = \"aUF-d1-5bR\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Window"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "窗口"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "視窗"}}}}, "AYu-sK-qS6.title": {"comment": "Class = \"NSMenu\"; title = \"Main Menu\"; ObjectID = \"AYu-sK-qS6\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Main Menu"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "主菜单"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "主選單"}}}}, "B2E-9g-fPK.title": {"comment": "Class = \"NSMenuItem\"; title = \"Last Edited 2 Weeks Ago\"; ObjectID = \"B2E-9g-fPK\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Last Edited 2 Weeks Ago"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "上次编辑两周前"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "上次編輯兩周前"}}}}, "BCM-0i-fPP.title": {"comment": "Class = \"NSMenuItem\"; title = \"Heading 3\"; ObjectID = \"BCM-0i-fPP\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Heading 3"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "三级标题"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "三級標題"}}}}, "bib-Uj-vzu.title": {"comment": "Class = \"NSMenu\"; title = \"File\"; ObjectID = \"bib-Uj-vzu\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "File"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "文件"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "檔案"}}}}, "BlA-2z-1dH.title": {"comment": "Class = \"NSMenuItem\"; title = \"Keep Up to 100 Versions\"; ObjectID = \"BlA-2z-1dH\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Keep Up to 100 Versions"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "保留最多 100 个版本"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "保留最多 100 個版本"}}}}, "BLl-I2-80X.title": {"comment": "Class = \"NSMenuItem\"; title = \"Insert Link\"; ObjectID = \"BLl-I2-80X\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Insert Link"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "插入链接"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "插入連結"}}}}, "BOF-NM-1cW.title": {"comment": "Class = \"NSMenuItem\"; title = \"Preferences…\"; ObjectID = \"BOF-NM-1cW\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Preferences…"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "设置…"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "設定…"}}}}, "buJ-ug-pKt.title": {"comment": "Class = \"NSMenuItem\"; title = \"Use Selection for Find\"; ObjectID = \"buJ-ug-pKt\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Use Selection for Find"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "查找所选内容"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "檢索所選內容"}}}}, "Bvk-zs-CQB.title": {"comment": "Class = \"NSMenuItem\"; title = \"Statistics\"; ObjectID = \"Bvk-zs-CQB\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Statistics"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "统计数据"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "統計數字"}}}}, "Bw7-FT-i3A.title": {"comment": "Class = \"NSMenuItem\"; title = \"Save As…\"; ObjectID = \"Bw7-FT-i3A\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Save As…"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "存储为…"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "儲存為…"}}}}, "BxI-nv-Xa5.title": {"comment": "Class = \"NSMenu\"; title = \"Table of Contents\"; ObjectID = \"BxI-nv-Xa5\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Table of Contents"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "内容目录"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "內容目錄"}}}}, "BxS-9Q-HQd.title": {"comment": "Class = \"NSMenuItem\"; title = \"File Path\"; ObjectID = \"BxS-9Q-HQd\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "File Path"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "文件路径"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "檔案路徑"}}}}, "c8a-y6-VQd.title": {"comment": "Class = \"NSMenu\"; title = \"Transformations\"; ObjectID = \"c8a-y6-VQd\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Transformations"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "转换"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "轉換"}}}}, "Ckk-yw-fiv.title": {"comment": "Class = \"NSWindow\"; title = \"Window\"; ObjectID = \"Ckk-yw-fiv\";", "extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Window"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "窗口"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "視窗"}}}}, "crU-NP-BJu.title": {"comment": "Class = \"NSMenuItem\"; title = \"Smaller\"; ObjectID = \"crU-NP-BJu\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Smaller"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "较小"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "縮小"}}}}, "Cvo-jv-iDg.title": {"comment": "Class = \"NSMenuItem\"; title = \"Typewriter Mode\"; ObjectID = \"Cvo-jv-iDg\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Typewriter Mode"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "打字机模式"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "打字機模式"}}}}, "cwL-P1-jid.title": {"comment": "Class = \"NSMenuItem\"; title = \"Smart Links\"; ObjectID = \"cwL-P1-jid\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Smart Links"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "智能链接"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "智慧型連結"}}}}, "d9M-CD-aMd.title": {"comment": "Class = \"NSMenuItem\"; title = \"Make Lower Case\"; ObjectID = \"d9M-CD-aMd\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Make Lower Case"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "变为小写"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "小寫"}}}}, "dHZ-CH-KLC.title": {"comment": "Class = \"NSMenuItem\"; title = \"New Tab\"; ObjectID = \"dHZ-CH-KLC\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "New Tab"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "新建标签"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "新增標籤"}}}}, "dMs-cI-mzQ.title": {"comment": "Class = \"NSMenuItem\"; title = \"File\"; ObjectID = \"dMs-cI-mzQ\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "File"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "文件"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "檔案"}}}}, "dRJ-4n-Yzg.title": {"comment": "Class = \"NSMenuItem\"; title = \"Undo\"; ObjectID = \"dRJ-4n-Yzg\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Undo"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "撤销"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "還原"}}}}, "Dv1-io-Yv7.title": {"comment": "Class = \"NSMenuItem\"; title = \"Spelling and Grammar\"; ObjectID = \"Dv1-io-Yv7\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Spelling and Grammar"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "拼写和语法"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "拼字和文法檢查"}}}}, "DVo-aG-piG.title": {"comment": "Class = \"NSMenuItem\"; title = \"Close\"; ObjectID = \"DVo-aG-piG\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Close"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "关闭"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "結束"}}}}, "DZc-Ee-Asp.title": {"comment": "Class = \"NSMenuItem\"; title = \"Todo\"; ObjectID = \"DZc-Ee-Asp\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Todo"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "待办"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "待辦"}}}}, "EEg-eg-6lJ.title": {"comment": "Class = \"NSMenuItem\"; title = \"Line Endings\"; ObjectID = \"EEg-eg-6lJ\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Line Endings"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "行尾格式"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "行尾格式"}}}}, "enN-sJ-W90.title": {"comment": "Class = \"NSMenuItem\"; title = \"Font\"; ObjectID = \"enN-sJ-W90\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Font"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "字体"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "字體"}}}}, "EoI-17-lrY.title": {"comment": "Class = \"NSMenuItem\"; title = \"All Local Versions\"; ObjectID = \"EoI-17-lrY\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "All Local Versions"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "所有本地版本"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "所有本地版本"}}}}, "ErA-Lh-vDf.title": {"comment": "Class = \"NSMenuItem\"; title = \"Bigger\"; ObjectID = \"ErA-Lh-vDf\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Bigger"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "较大"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "放大"}}}}, "ET1-7i-3N1.title": {"comment": "Class = \"NSMenuItem\"; title = \"Table\"; ObjectID = \"ET1-7i-3N1\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Table"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "表格"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "表格"}}}}, "ex0-rS-Zlm.title": {"comment": "Class = \"NSMenuItem\"; title = \"Read Only Mode\"; ObjectID = \"ex0-rS-Zlm\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Read Only Mode"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "只读模式"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "只讀模式"}}}}, "F2S-fz-NVQ.title": {"comment": "Class = \"NSMenu\"; title = \"Help\"; ObjectID = \"F2S-fz-NVQ\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Help"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "帮助"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "輔助說明"}}}}, "FeM-D8-WVr.title": {"comment": "Class = \"NSMenu\"; title = \"Substitutions\"; ObjectID = \"FeM-D8-WVr\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Substitutions"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "替换"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "替代"}}}}, "FKE-Sm-Kum.title": {"comment": "Class = \"NSMenuItem\"; title = \"MarkEdit Help\"; ObjectID = \"FKE-Sm-Kum\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "MarkEdit Help"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "MarkEdit 帮助"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "MarkEdit 輔助說明"}}}}, "fyH-S9-f9N.title": {"comment": "Class = \"NSMenuItem\"; title = \"Open Documents Folder\"; ObjectID = \"fyH-S9-f9N\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Open Documents Folder"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "打开文档目录"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "開啟文件目錄"}}}}, "Gbu-oy-t2b.title": {"comment": "Class = \"NSMenuItem\"; title = \"Select Previous Section\"; ObjectID = \"Gbu-oy-t2b\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Select Previous Section"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "选择上个章节"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "選擇上個章節"}}}}, "GdB-IP-6C8.title": {"comment": "Class = \"NSMenuItem\"; title = \"Open In...\"; ObjectID = \"GdB-IP-6C8\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Open In..."}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "使用应用打开…"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "使用應用打開…"}}}}, "gfx-t1-1ED.title": {"comment": "Class = \"NSMenuItem\"; title = \"Heading 6\"; ObjectID = \"gfx-t1-1ED\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Heading 6"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "六级标题"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "六級標題"}}}}, "Ghc-zK-wBC.title": {"comment": "Class = \"NSMenuItem\"; title = \"Reset to Default\"; ObjectID = \"Ghc-zK-wBC\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Reset to De<PERSON>ult"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "重置为默认"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "重置為預設"}}}}, "gHK-tW-hmK.title": {"comment": "Class = \"NSMenuItem\"; title = \"Go Back\"; ObjectID = \"gHK-tW-hmK\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Go Back"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "后退"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "後退"}}}}, "gVA-U4-sdL.title": {"comment": "Class = \"NSMenuItem\"; title = \"Paste\"; ObjectID = \"gVA-U4-sdL\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Paste"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "粘贴"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "貼上"}}}}, "gwQ-Vq-avP.title": {"comment": "Class = \"NSMenuItem\"; title = \"Reveal in Finder\"; ObjectID = \"gwQ-Vq-avP\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Reveal in Finder"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "在访达中找到"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "在 Finder 中找到"}}}}, "H8h-7b-M4v.title": {"comment": "Class = \"NSMenuItem\"; title = \"View\"; ObjectID = \"H8h-7b-M4v\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "View"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "显示"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "顯示方式"}}}}, "HFo-cy-zxI.title": {"comment": "Class = \"NSMenuItem\"; title = \"Show Spelling and Grammar\"; ObjectID = \"HFo-cy-zxI\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Show Spelling and Grammar"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "显示拼写和语法"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "顯示拼字和文法檢查"}}}}, "HFQ-gK-NFA.title": {"comment": "Class = \"NSMenuItem\"; title = \"Text Replacement\"; ObjectID = \"HFQ-gK-NFA\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Text Replacement"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "文本替换"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "替代文字"}}}}, "hLx-6U-DoY.title": {"comment": "Class = \"NSMenuItem\"; title = \"Last Edited 6 Months Ago\"; ObjectID = \"hLx-6U-DoY\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Last Edited 6 Months Ago"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "上次编辑六个月前"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "上次編輯六個月前"}}}}, "hQb-2v-fYv.title": {"comment": "Class = \"NSMenuItem\"; title = \"Smart Quotes\"; ObjectID = \"hQb-2v-fYv\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Smart Quotes"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "智能引号"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "智慧型引號"}}}}, "hw9-Pl-u8s.title": {"comment": "Class = \"NSMenuItem\"; title = \"Grant Folder Access\"; ObjectID = \"hw9-Pl-u8s\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Grant Folder Access"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "授予目录权限"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "授予目錄許可權"}}}}, "HyV-fh-RgO.title": {"comment": "Class = \"NSMenu\"; title = \"View\"; ObjectID = \"HyV-fh-RgO\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "View"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "显示"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "顯示方式"}}}}, "hz2-CU-CR7.title": {"comment": "Class = \"NSMenuItem\"; title = \"Check Document Now\"; ObjectID = \"hz2-CU-CR7\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Check Document Now"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "立即检查文稿"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "立即檢查文件"}}}}, "hz9-B4-Xy5.title": {"comment": "Class = \"NSMenu\"; title = \"Services\"; ObjectID = \"hz9-B4-Xy5\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Services"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "服务"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "服務"}}}}, "i0b-gh-V2Y.title": {"comment": "Class = \"NSMenuItem\"; title = \"Zoom Out\"; ObjectID = \"i0b-gh-V2Y\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Zoom Out"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "缩小"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "縮小"}}}}, "IAo-SY-fd9.title": {"comment": "Class = \"NSMenuItem\"; title = \"Open…\"; ObjectID = \"IAo-SY-fd9\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Open…"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "打开…"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "打開…"}}}}, "Ibb-aP-jnQ.title": {"comment": "Class = \"NSMenuItem\"; title = \"Code\"; ObjectID = \"Ibb-aP-jnQ\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Code"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "代码"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "程式碼"}}}}, "iIq-nl-rwB.title": {"comment": "Class = \"NSMenuItem\"; title = \"Export to HTML\"; ObjectID = \"iIq-nl-rwB\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Export to HTML"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "导出到 HTML"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "輸出為 HTML"}}}}, "InE-bU-3FX.title": {"comment": "Class = \"NSMenuItem\"; title = \"Select Line\"; ObjectID = \"InE-bU-3FX\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Select Line"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "选择整行"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "選擇整行"}}}}, "IPm-0a-pwK.title": {"comment": "Class = \"NSMenuItem\"; title = \"Open Menu\"; ObjectID = \"IPm-0a-pwK\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Open Menu"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "打开菜单"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "打開選單"}}}}, "j7w-ok-RLg.title": {"comment": "Class = \"NSMenuItem\"; title = \"Grant Folder Access\"; ObjectID = \"j7w-ok-RLg\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Grant Folder Access"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "授予目录权限"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "授予目錄許可權"}}}}, "jNi-CL-4bf.title": {"comment": "Class = \"NSMenuItem\"; title = \"Customization Guide\"; ObjectID = \"jNi-CL-4bf\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Customization Guide"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "自定义指南"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "自定義指南"}}}}, "jv9-dC-jQp.title": {"comment": "Class = \"NSMenuItem\"; title = \"Copy Path\"; ObjectID = \"jv9-dC-jQp\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Copy Path"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "拷贝路径"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "拷貝路徑"}}}}, "k8z-Oj-5dp.title": {"comment": "Class = \"NSMenuItem\"; title = \"Move Line Up\"; ObjectID = \"k8z-Oj-5dp\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Move Line Up"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "向上移动行"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "向上移動行"}}}}, "K67-Wt-aYF.title": {"comment": "Class = \"NSMenuItem\"; title = \"Italic\"; ObjectID = \"K67-Wt-aYF\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Italic"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "斜体"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "斜體"}}}}, "KaW-ft-85H.title": {"comment": "Class = \"NSMenuItem\"; title = \"Revert to Saved\"; ObjectID = \"KaW-ft-85H\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Revert to Saved"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "复原到已存储版本"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "復原到已儲存版本"}}}}, "Kd2-mp-pUS.title": {"comment": "Class = \"NSMenuItem\"; title = \"Show All\"; ObjectID = \"Kd2-mp-pUS\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Show All"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "全部显示"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "顯示全部"}}}}, "kMr-G4-Kek.title": {"comment": "Class = \"NSMenuItem\"; title = \"Heading 1\"; ObjectID = \"kMr-G4-Kek\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Heading 1"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "一级标题"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "一級標題"}}}}, "KrK-C3-Ckx.title": {"comment": "Class = \"NSMenu\"; title = \"Developer\"; ObjectID = \"KrK-C3-Ckx\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Developer"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "开发者"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "開發者"}}}}, "LE2-aR-0XJ.title": {"comment": "Class = \"NSMenuItem\"; title = \"Bring All to Front\"; ObjectID = \"LE2-aR-0XJ\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Bring All to Front"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "前置全部窗口"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "將此程式所有視窗移至最前"}}}}, "lVU-H3-VpV.title": {"comment": "Class = \"NSMenuItem\"; title = \"Format\"; ObjectID = \"lVU-H3-VpV\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Format"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "格式"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "格式"}}}}, "mc2-DH-5Iy.title": {"comment": "Class = \"NSMenuItem\"; title = \"Windows (CRLF)\"; ObjectID = \"mc2-DH-5Iy\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Windows (CRLF)"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "Windows (CRLF)"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "Windows (CRLF)"}}}}, "mGH-Xd-vai.title": {"comment": "Class = \"NSMenuItem\"; title = \"Extensions\"; ObjectID = \"mGH-Xd-vai\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Extensions"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "扩展功能"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "擴充功能"}}}}, "mK6-2p-4JG.title": {"comment": "Class = \"NSMenuItem\"; title = \"Check Grammar With Spelling\"; ObjectID = \"mK6-2p-4JG\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Check Grammar With Spelling"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "检查拼写和语法"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "檢查拼字文法"}}}}, "My7-8t-jlU.title": {"comment": "Class = \"NSMenuItem\"; title = \"Complete\"; ObjectID = \"My7-8t-jlU\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Complete"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "补全"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "補全"}}}}, "N2l-0X-yEw.title": {"comment": "Class = \"NSMenuItem\"; title = \"Insert Image\"; ObjectID = \"N2l-0X-yEw\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Insert Image"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "插入图片"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "插入圖片"}}}}, "nkD-z6-yjV.title": {"comment": "Class = \"NSMenu\"; title = \"Extensions\"; ObjectID = \"nkD-z6-yjV\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Extensions"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "扩展功能"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "擴充功能"}}}}, "NMo-om-nkz.title": {"comment": "Class = \"NSMenuItem\"; title = \"Services\"; ObjectID = \"NMo-om-nkz\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Services"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "服务"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "服務"}}}}, "oas-Oc-fiZ.title": {"comment": "Class = \"NSMenu\"; title = \"Open Recent\"; ObjectID = \"oas-Oc-fiZ\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Open Recent"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "打开最近使用"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "打開最近使用過的檔案"}}}}, "OB4-Cz-Y5L.title": {"comment": "Class = \"NSMenuItem\"; title = \"Toggle Block Comment\"; ObjectID = \"OB4-Cz-Y5L\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Toggle Block Comment"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "切换块注释"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "切換塊註解"}}}}, "oc0-ce-Yrd.title": {"comment": "Class = \"NSMenuItem\"; title = \"Export to PDF\"; ObjectID = \"oc0-ce-Yrd\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Export to PDF"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "导出到 PDF"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "輸出為 PDF"}}}}, "Olw-nP-bQN.title": {"comment": "Class = \"NSMenuItem\"; title = \"Hide MarkEdit\"; ObjectID = \"Olw-nP-bQN\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "<PERSON><PERSON>"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "隐藏 MarkEdit"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "隱藏 MarkEdit"}}}}, "ORv-QM-Kju.title": {"comment": "Class = \"NSMenuItem\"; title = \"Export to EPUB\"; ObjectID = \"ORv-QM-Kju\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Export to EPUB"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "导出到 EPUB"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "輸出為 EPUB"}}}}, "oTO-fv-8dv.title": {"comment": "Class = \"NSMenuItem\"; title = \"Zoom In\"; ObjectID = \"oTO-fv-8dv\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Zoom In"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "放大"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "放大"}}}}, "OwM-mh-QMV.title": {"comment": "Class = \"NSMenuItem\"; title = \"Find Previous\"; ObjectID = \"OwM-mh-QMV\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Find Previous"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "查找上一个"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "尋找上一個"}}}}, "OY7-WF-poV.title": {"comment": "Class = \"NSMenuItem\"; title = \"Minimize\"; ObjectID = \"OY7-WF-poV\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Minimize"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "最小化"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "縮到最小"}}}}, "Oyz-dy-DGm.title": {"comment": "Class = \"NSMenuItem\"; title = \"Stop Speaking\"; ObjectID = \"Oyz-dy-DGm\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Stop Speaking"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "停止朗读"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "停止朗讀"}}}}, "P3N-ql-hAd.title": {"comment": "Class = \"NSMenuItem\"; title = \"Horizontal Rule\"; ObjectID = \"P3N-ql-hAd\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Horizontal Rule"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "水平分割线"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "水平分割線"}}}}, "P4a-Ym-fhL.title": {"comment": "Class = \"NSMenuItem\"; title = \"Heading 4\"; ObjectID = \"P4a-Ym-fhL\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Heading 4"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "四级标题"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "四級標題"}}}}, "P57-5f-z5e.title": {"comment": "Class = \"NSMenuItem\"; title = \"Toggle Line Comment\"; ObjectID = \"P57-5f-z5e\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Toggle Line Comment"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "切换行注释"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "切換行註解"}}}}, "pa3-QI-u2k.title": {"comment": "Class = \"NSMenuItem\"; title = \"Delete\"; ObjectID = \"pa3-QI-u2k\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Delete"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "删除"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "刪除"}}}}, "PNm-e0-aoZ.title": {"comment": "Class = \"NSMenuItem\"; title = \"Last Edited 1 Year Ago\"; ObjectID = \"PNm-e0-aoZ\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Last Edited 1 Year Ago"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "上次编辑一年前"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "上次編輯一年前"}}}}, "pTm-hL-bI5.title": {"comment": "Class = \"NSMenuItem\"; title = \"Copy Line Up\"; ObjectID = \"pTm-hL-bI5\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Copy Line Up"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "向上拷贝行"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "向上拷貝行"}}}}, "pxx-59-PXV.title": {"comment": "Class = \"NSMenuItem\"; title = \"Save…\"; ObjectID = \"pxx-59-PXV\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Save…"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "存储…"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "儲存…"}}}}, "q09-fT-Sye.title": {"comment": "Class = \"NSMenuItem\"; title = \"Find Next\"; ObjectID = \"q09-fT-Sye\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Find Next"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "查找下一个"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "尋找下一個"}}}}, "qIS-W8-SiK.title": {"comment": "Class = \"NSMenuItem\"; title = \"Page Setup…\"; ObjectID = \"qIS-W8-SiK\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Page Setup…"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "页面设置…"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "設定頁面…"}}}}, "qjf-Yo-EMd.title": {"comment": "Class = \"NSMenuItem\"; title = \"Developer\"; ObjectID = \"qjf-Yo-EMd\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Developer"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "开发者"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "開發者"}}}}, "qOk-v8-kKx.title": {"comment": "Class = \"NSMenuItem\"; title = \"macOS / Unix (LF)\"; ObjectID = \"qOk-v8-kKx\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "macOS / Unix (LF)"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "macOS / Unix (LF)"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "macOS / Unix (LF)"}}}}, "QOM-rI-Bxj.title": {"comment": "Class = \"NSMenuItem\"; title = \"Code Block\"; ObjectID = \"QOM-rI-Bxj\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Code Block"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "代码块"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "程式碼塊"}}}}, "QVA-GW-ruk.title": {"comment": "Class = \"NSMenuItem\"; title = \"Export to RTF\"; ObjectID = \"QVA-GW-ruk\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Export to RTF"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "导出到 RTF"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "輸出為 RTF"}}}}, "QZC-LS-WMo.title": {"comment": "Class = \"NSMenu\"; title = \"Copy Pandoc Command\"; ObjectID = \"QZC-LS-WMo\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Copy Pandoc Command"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "拷贝 Pandoc 指令"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "拷貝 Pandoc 指令"}}}}, "R4o-n2-Eq4.title": {"comment": "Class = \"NSMenuItem\"; title = \"Zoom\"; ObjectID = \"R4o-n2-Eq4\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Zoom"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "缩放"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "縮放"}}}}, "R9C-UY-Nnm.title": {"comment": "Class = \"NSMenuItem\"; title = \"Expand Selection\"; ObjectID = \"R9C-UY-Nnm\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Expand Selection"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "扩大选择区域"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "擴大選擇區域"}}}}, "rbD-Rh-wIN.title": {"comment": "Class = \"NSMenuItem\"; title = \"Check Spelling While Typing\"; ObjectID = \"rbD-Rh-wIN\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Check Spelling While Typing"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "键入时检查拼写"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "在輸入時同步檢查拼字"}}}}, "REb-f5-kpc.title": {"comment": "Class = \"NSMenuItem\"; title = \"Table of Contents\"; ObjectID = \"REb-f5-kpc\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Table of Contents"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "内容目录"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "內容目錄"}}}}, "rgM-f4-ycn.title": {"comment": "Class = \"NSMenuItem\"; title = \"Smart Dashes\"; ObjectID = \"rgM-f4-ycn\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "<PERSON> Dashes"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "智能破折号"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "智慧型破折號"}}}}, "Rjg-ZG-COT.title": {"comment": "Class = \"NSMenu\"; title = \"Purge History Versions\"; ObjectID = \"Rjg-ZG-COT\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Purge History Versions"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "清理历史版本"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "清理歷史版本"}}}}, "Rpw-oR-sIY.title": {"comment": "Class = \"NSMenuItem\"; title = \"Heading 2\"; ObjectID = \"Rpw-oR-sIY\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Heading 2"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "二级标题"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "二級標題"}}}}, "Ruw-6m-B2m.title": {"comment": "Class = \"NSMenuItem\"; title = \"Select All\"; ObjectID = \"Ruw-6m-B2m\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Select All"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "全选"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "全選"}}}}, "S0p-oC-mLd.title": {"comment": "Class = \"NSMenuItem\"; title = \"Jump to Selection\"; ObjectID = \"S0p-oC-mLd\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Jump to Selection"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "跳到所选内容"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "跳至所選範圍"}}}}, "s40-D9-CoM.title": {"comment": "Class = \"NSMenuItem\"; title = \"Check for Updates...\"; ObjectID = \"s40-D9-CoM\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Check for Updates..."}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "检查更新…"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "檢查更新…"}}}}, "sPG-vJ-QST.title": {"comment": "Class = \"NSMenu\"; title = \"Reopen with Encoding\"; ObjectID = \"sPG-vJ-QST\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Reopen with Encoding"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "以编码重新打开"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "以編碼重新打開"}}}}, "Sqc-7N-3lD.title": {"comment": "Class = \"NSMenuItem\"; title = \"Math Block\"; ObjectID = \"Sqc-7N-3lD\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Math Block"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "块状公式"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "塊狀公式"}}}}, "Sqh-CT-2fi.title": {"comment": "Class = \"NSMenuItem\"; title = \"Actual Size\"; ObjectID = \"Sqh-CT-2fi\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Actual Size"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "实际大小"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "實際大小"}}}}, "Td7-aD-5lo.title": {"comment": "Class = \"NSMenu\"; title = \"Window\"; ObjectID = \"Td7-aD-5lo\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Window"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "窗口"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "視窗"}}}}, "toe-mI-2pC.title": {"comment": "Class = \"NSMenuItem\"; title = \"Heading 5\"; ObjectID = \"toe-mI-2pC\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Heading 5"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "五级标题"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "五級標題"}}}}, "tqa-Ue-2Ms.title": {"comment": "Class = \"NSMenuItem\"; title = \"Commands\"; ObjectID = \"tqa-Ue-2Ms\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Commands"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "操作指令"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "操作指令"}}}}, "tRc-n8-cdB.title": {"comment": "Class = \"NSMenuItem\"; title = \"Shrink Selection\"; ObjectID = \"tRc-n8-cdB\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Shrink Selection"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "缩小选择区域"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "縮小選擇區域"}}}}, "Tuy-2U-frg.title": {"comment": "Class = \"NSMenu\"; title = \"Open In...\"; ObjectID = \"Tuy-2U-frg\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Open In..."}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "使用应用打开…"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "使用應用打開…"}}}}, "tXI-mr-wws.title": {"comment": "Class = \"NSMenuItem\"; title = \"Open Recent\"; ObjectID = \"tXI-mr-wws\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Open Recent"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "打开最近使用"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "打開最近使用過的檔案"}}}}, "U9S-1R-P8I.title": {"comment": "Class = \"NSMenuItem\"; title = \"Copy Line Down\"; ObjectID = \"U9S-1R-P8I\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Copy Line Down"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "向下拷贝行"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "向下拷貝行"}}}}, "UEZ-Bs-lqG.title": {"comment": "Class = \"NSMenuItem\"; title = \"Capitalize\"; ObjectID = \"UEZ-Bs-lqG\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Capitalize"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "首字母大写"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "大寫"}}}}, "uHE-Ma-KIM.title": {"comment": "Class = \"NSMenuItem\"; title = \"Indent More\"; ObjectID = \"uHE-Ma-KIM\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Indent More"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "增加缩进"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "增加縮排"}}}}, "Ukm-Sm-nrt.title": {"comment": "Class = \"NSMenuItem\"; title = \"Export to Word Docx\"; ObjectID = \"Ukm-Sm-nrt\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Export to Word Docx"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "导出到 Word Docx"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "輸出為 Word Docx"}}}}, "uKp-pC-7O6.title": {"comment": "Class = \"NSMenuItem\"; title = \"Issue Tracker\"; ObjectID = \"uKp-pC-7O6\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Issue Tracker"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "问题追踪系统"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "問題追蹤系統"}}}}, "uQy-DD-JDr.title": {"comment": "Class = \"NSMenu\"; title = \"MarkEdit\"; ObjectID = \"uQy-DD-JDr\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "MarkE<PERSON>"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "MarkE<PERSON>"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "MarkE<PERSON>"}}}}, "uRl-iY-unG.title": {"comment": "Class = \"NSMenuItem\"; title = \"Cut\"; ObjectID = \"uRl-iY-unG\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Cut"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "剪切"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "剪下"}}}}, "vbS-fI-FfG.title": {"comment": "Class = \"NSMenuItem\"; title = \"Keep Up to 50 Versions\"; ObjectID = \"vbS-fI-FfG\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Keep Up to 50 Versions"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "保留最多 50 个版本"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "保留最多 50 個版本"}}}}, "Vdr-fp-XzO.title": {"comment": "Class = \"NSMenuItem\"; title = \"Hide Others\"; ObjectID = \"Vdr-fp-XzO\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Hide Others"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "隐藏其他"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "隱藏其他"}}}}, "vhv-qY-5Gc.title": {"comment": "Class = \"NSMenuItem\"; title = \"Ordered List\"; ObjectID = \"vhv-qY-5Gc\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Ordered List"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "有序列表"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "有序列表"}}}}, "VIj-UD-bGA.title": {"comment": "Class = \"NSMenuItem\"; title = \"Select Next Occurrence\"; ObjectID = \"VIj-UD-bGA\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Select Next Occurrence"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "选择下个相同项"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "選擇下個相同項"}}}}, "vmV-6d-7jI.title": {"comment": "Class = \"NSMenuItem\"; title = \"Make Upper Case\"; ObjectID = \"vmV-6d-7jI\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Make Upper Case"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "变为大写"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "大寫"}}}}, "vNY-rz-j42.title": {"comment": "Class = \"NSMenuItem\"; title = \"Clear Menu\"; ObjectID = \"vNY-rz-j42\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Clear Menu"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "清除菜单"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "清除選單"}}}}, "VwK-Nx-wgD.title": {"comment": "Class = \"NSMenu\"; title = \"Font\"; ObjectID = \"VwK-Nx-wgD\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Font"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "字体"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "字體"}}}}, "w9R-Kp-jCa.title": {"comment": "Class = \"NSMenuItem\"; title = \"Indent Less\"; ObjectID = \"w9R-Kp-jCa\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Indent Less"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "减少缩进"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "減少縮排"}}}}, "W48-6f-4Dl.title": {"comment": "Class = \"NSMenu\"; title = \"Edit\"; ObjectID = \"W48-6f-4Dl\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Edit"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "编辑"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "編輯"}}}}, "WAg-qh-q9G.title": {"comment": "Class = \"NSMenuItem\"; title = \"Open Documents Folder\"; ObjectID = \"WAg-qh-q9G\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Open Documents Folder"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "打开文档目录"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "開啟文件目錄"}}}}, "WAq-55-Lug.title": {"comment": "Class = \"NSMenuItem\"; title = \"Bold\"; ObjectID = \"WAq-55-Lug\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Bold"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "粗体"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "粗體"}}}}, "Was-JA-tGl.title": {"comment": "Class = \"NSMenuItem\"; title = \"New\"; ObjectID = \"Was-JA-tGl\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "New"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "新建"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "新增"}}}}, "WbI-eI-YNA.title": {"comment": "Class = \"NSMenuItem\"; title = \"Inspect Element\"; ObjectID = \"WbI-eI-YNA\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Inspect Element"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "审查元素"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "審查元素"}}}}, "wBj-kZ-XmW.title": {"comment": "Class = \"NSMenuItem\"; title = \"Go to Line...\"; ObjectID = \"wBj-kZ-XmW\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Go to Line..."}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "跳转到行…"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "跳轉到行…"}}}}, "wIF-eb-cez.title": {"comment": "Class = \"NSMenuItem\"; title = \"Math\"; ObjectID = \"wIF-eb-cez\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Math"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "行内公式"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "行內公式"}}}}, "wpr-3q-Mcd.title": {"comment": "Class = \"NSMenuItem\"; title = \"Help\"; ObjectID = \"wpr-3q-Mcd\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Help"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "帮助"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "輔助說明"}}}}, "x3v-GG-iWU.title": {"comment": "Class = \"NSMenuItem\"; title = \"Copy\"; ObjectID = \"x3v-GG-iWU\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Copy"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "拷贝"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "拷貝"}}}}, "XQw-pS-pD7.title": {"comment": "Class = \"NSMenuItem\"; title = \"Reopen with Encoding\"; ObjectID = \"XQw-pS-pD7\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Reopen with Encoding"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "以编码重新打开"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "以編碼重新打開"}}}}, "xrE-MZ-jX0.title": {"comment": "Class = \"NSMenuItem\"; title = \"Speech\"; ObjectID = \"xrE-MZ-jX0\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Speech"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "语音"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "語音"}}}}, "XUq-5x-gZk.title": {"comment": "Class = \"NSMenuItem\"; title = \"Go Forward\"; ObjectID = \"XUq-5x-gZk\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Go Forward"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "前进"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "前進"}}}}, "Xz5-n4-O0W.title": {"comment": "Class = \"NSMenuItem\"; title = \"Find…\"; ObjectID = \"Xz5-n4-O0W\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Find…"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "查找…"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "尋找…"}}}}, "YEy-JH-Tfz.title": {"comment": "Class = \"NSMenuItem\"; title = \"Find and Replace…\"; ObjectID = \"YEy-JH-Tfz\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Find and Replace…"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "查找和替换…"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "尋找與取代…"}}}}, "yfL-ea-k7X.title": {"comment": "Class = \"NSMenuItem\"; title = \"Purge History Versions\"; ObjectID = \"yfL-ea-k7X\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Purge History Versions"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "清理历史版本"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "清理歷史版本"}}}}, "Ynk-f8-cLZ.title": {"comment": "Class = \"NSMenuItem\"; title = \"Start Speaking\"; ObjectID = \"Ynk-f8-cLZ\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Start Speaking"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "开始朗读"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "開始朗讀"}}}}, "yOb-Z8-aCk.title": {"comment": "Class = \"NSMenuItem\"; title = \"Last Edited 1 Month Ago\"; ObjectID = \"yOb-Z8-aCk\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Last Edited 1 Month Ago"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "上次编辑一个月前"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "上次編輯一個月前"}}}}, "yv7-Am-Rb2.title": {"comment": "Class = \"NSMenuItem\"; title = \"Learn More...\"; ObjectID = \"yv7-Am-Rb2\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Learn More..."}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "了解更多…"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "瞭解更多…"}}}}, "yy7-cF-JbT.title": {"comment": "Class = \"NSMenu\"; title = \"Format\"; ObjectID = \"yy7-cF-JbT\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Format"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "格式"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "格式"}}}}, "z5V-GU-gP0.title": {"comment": "Class = \"NSMenu\"; title = \"Headers\"; ObjectID = \"z5V-GU-gP0\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Headers"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "标题"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "標題"}}}}, "z6F-FW-3nz.title": {"comment": "Class = \"NSMenuItem\"; title = \"Show Substitutions\"; ObjectID = \"z6F-FW-3nz\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Show Substitutions"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "显示替换"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "顯示替代視窗"}}}}, "z7z-Qk-jsC.title": {"comment": "Class = \"NSMenuItem\"; title = \"Strikethrough\"; ObjectID = \"z7z-Qk-jsC\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Strikethrough"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "删除线"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "刪除線"}}}}, "ZM6-yO-uqe.title": {"comment": "Class = \"NSMenuItem\"; title = \"Select Next Section\"; ObjectID = \"ZM6-yO-uqe\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Select Next Section"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "选择下个章节"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "選擇下個章節"}}}}, "zsS-up-p9L.title": {"comment": "Class = \"NSMenuItem\"; title = \"Select All Occurrences\"; ObjectID = \"zsS-up-p9L\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Select All Occurrences"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "选择所有相同项"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "選擇所有相同項"}}}}, "zVr-EF-hMY.title": {"comment": "Class = \"NSMenuItem\"; title = \"Folder Path\"; ObjectID = \"zVr-EF-hMY\";", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Folder Path"}}, "zh-Hans": {"stringUnit": {"state": "translated", "value": "目录路径"}}, "zh-Hant": {"stringUnit": {"state": "translated", "value": "目錄路徑"}}}}}, "version": "1.0"}