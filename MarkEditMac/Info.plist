<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>Markdown</string>
			<key>LSTypeIsPackage</key>
			<false/>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>md</string>
				<string>markdown</string>
				<string>mdown</string>
				<string>mdwn</string>
				<string>mkdn</string>
				<string>mkd</string>
				<string>mdoc</string>
				<string>mdtext</string>
				<string>mdtxt</string>
			</array>
			<key>LSItemContentTypes</key>
			<array>
				<string>net.daringfireball.markdown</string>
				<string>net.ia.markdown</string>
				<string>app.markedit.md</string>
				<string>app.markedit.markdown</string>
				<string>app.markedit.txt</string>
				<string>public.markdown</string>
				<string>org.quarto.qmarkdown</string>
				<string>com.unknown.md</string>
				<string>com.rstudio.rmarkdown</string>
				<string>com.nutstore.down</string>
				<string>dyn.ah62d4rv4ge81e5pe</string>
				<string>dyn.ah62d4rv4ge8043a</string>
				<string>dyn.ah62d4rv4ge81c5pe</string>
			</array>
			<key>NSDocumentClass</key>
			<string>$(PRODUCT_MODULE_NAME).EditorDocument</string>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>Text Bundle</string>
			<key>LSTypeIsPackage</key>
			<integer>1</integer>
			<key>CFBundleTypeIconSystemGenerated</key>
			<integer>1</integer>
			<key>NSIsRelatedItemType</key>
			<true/>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Default</string>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>textbundle</string>
			</array>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.textbundle.package</string>
			</array>
			<key>NSDocumentClass</key>
			<string>$(PRODUCT_MODULE_NAME).EditorDocument</string>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>Plain Text</string>
			<key>LSTypeIsPackage</key>
			<false/>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Default</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.plain-text</string>
				<string>public.utf8-plain-text</string>
				<string>public.utf16-plain-text</string>
				<string>public.text</string>
			</array>
			<key>NSDocumentClass</key>
			<string>$(PRODUCT_MODULE_NAME).EditorDocument</string>
		</dict>
	</array>
	<key>NSAppleScriptEnabled</key>
	<true/>
	<key>OSAScriptingDefinition</key>
	<string>MarkEdit.sdef</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLIconFile</key>
			<string></string>
			<key>CFBundleURLName</key>
			<string>markedit</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>markedit</string>
			</array>
		</dict>
	</array>
	<key>UTImportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.plain-text</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Markdown</string>
			<key>UTTypeIdentifier</key>
			<string>net.daringfireball.markdown</string>
			<key>UTTypeReferenceURL</key>
			<string>https://daringfireball.net/projects/markdown/</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>md</string>
					<string>markdown</string>
					<string>mdown</string>
					<string>mdwn</string>
					<string>mkdn</string>
					<string>mkd</string>
					<string>mdoc</string>
					<string>mdtext</string>
					<string>mdtxt</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>com.apple.package</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Text Bundle</string>
			<key>UTTypeIdentifier</key>
			<string>org.textbundle.package</string>
			<key>UTTypeReferenceURL</key>
			<string>http://www.textbundle.org</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>textbundle</string>
				</array>
			</dict>
		</dict>
	</array>
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.plain-text</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Markdown</string>
			<key>UTTypeIdentifier</key>
			<string>app.markedit.md</string>
			<key>UTTypeReferenceURL</key>
			<string>https://daringfireball.net/projects/markdown/</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>md</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.plain-text</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Markdown</string>
			<key>UTTypeIdentifier</key>
			<string>app.markedit.markdown</string>
			<key>UTTypeReferenceURL</key>
			<string>https://daringfireball.net/projects/markdown/</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>markdown</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.plain-text</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Markdown</string>
			<key>UTTypeIdentifier</key>
			<string>app.markedit.txt</string>
			<key>UTTypeReferenceURL</key>
			<string>https://daringfireball.net/projects/markdown/</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>txt</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.text</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Plain Text</string>
			<key>UTTypeIdentifier</key>
			<string>public.plain-text</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>txt</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>com.apple.package</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Text Bundle</string>
			<key>UTTypeIdentifier</key>
			<string>org.textbundle.package</string>
			<key>UTTypeReferenceURL</key>
			<string>http://www.textbundle.org</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>textbundle</string>
				</array>
			</dict>
		</dict>
	</array>
</dict>
</plist>
