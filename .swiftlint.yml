disabled_rules:

  # Rationale: Arbitrary restriction
  - cyclomatic_complexity

  # Rationale: Allow String(data: data, encoding: .utf8) to be used
  - non_optional_string_data_conversion

  # Rationale: There are cases where you may want to declare the string enum value explicitly
  - redundant_string_enum_value

  # Rationale: Allow optional properties in typescript objects
  - discouraged_optional_boolean

opt_in_rules:

  # Rationale: When using map, we think of it being used to transform a current array into something else
  - array_init

  # Rationale: Provides consistency in coding style
  - attributes

  # Rationale: Provides consistency in coding style and follows modern practices of the language
  - block_based_kvo

  # Rationale: Prevents retain cycles
  - class_delegate_protocol

  # Rationale: Provides consistency in coding style
  - closing_brace

  # Rationale: Provides consistency in coding style
  - closure_parameter_position

  # Rationale: Provides consistency in coding style
  - closure_spacing

  # Rationale: Provides consistency in coding style
  - colon

  # Rationale: Provides consistency in coding style
  - comma

  # Rationale: Provides consistency in coding style
  - compiler_protocol_init

  # Rationale: A more clear and consise way to check if something exists
  - contains_over_filter_count

  # Rationale: A more clear and consise way to check if something exists
  - contains_over_filter_is_empty

  # Rationale: A more clear and consise way to check if something exists
  - contains_over_first_not_nil

  # Rationale: A more clear and consise way to check if a range exists
  - contains_over_range_nil_comparison

  # Rationale: Provides consistency in coding style and follows modern practices of the language
  - control_statement

  # Rationale: Encourages proper memory practices
  - discarded_notification_center_observer

  # Rationale: Prevents coder error
  - discouraged_direct_init

  # Rationale: Imports are not required more than once.
  - duplicate_imports

  # Rationale: Prevents coder error
  - dynamic_inline

  # Rationale: Provides consistency in coding style
  - empty_collection_literal

  # Rationale: Provides consistency in coding style and follows modern practices of the language
  - empty_count

  # Rationale: Provides consistency in coding style and brevity.
  - empty_enum_arguments

  # Rationale: Provides consistency in coding style and follows modern practices of the language
  - empty_parameters

  # Rationale: Provides consistency in coding style
  - empty_parentheses_with_trailing_closure

  # Rationale: Provides consistency in coding style and follows modern practices of the language
  - empty_string

  # Rationale: Provides consistency in coding style
  - explicit_init

  # Rationale: Prevents coder error
  - fallthrough

  # Rationale: Encourages better documentation
  - fatal_error_message

  # Rationale: Encourages using the right API to solve a problem
  - first_where

  # Rationale: Provides consistency in coding style
  - flatmap_over_map_reduce

  # Rationale: Encourages using the right API to solve a problem
  - for_where

  # Rationale: Prevents coder error, doesn't crash, makes coder be explicit about their assumptions
  - force_cast

  # Rationale: Prevents coder error, doesn't crash, makes coder be explicit about their assumptions
  - force_try

  # Rationale: Prevents coder error, doesn't crash, makes coder be explicit about their assumptions
  - force_unwrapping

  # Rationale: Provides consistency in coding style and brevity.
  - implicit_getter

  # Rationale: Prevents coder error, doesn't crash, makes coder be explicit about their assumptions
  - implicitly_unwrapped_optional

  # Rationale: Encourages using the right API to solve a problem
  - is_disjoint

  # Rationale: Provides clarity and consistency by using the default parameter
  - joined_default_parameter

  # Rationale: Provides consistency in coding style
  - last_where

  # Rationale: Provides consistency in coding style
  - leading_whitespace

  # Rationale: Provides consistency in coding style and follows modern practices of the language
  - legacy_cggeometry_functions

  # Rationale: Provides consistency in coding style and follows modern practices of the language
  - legacy_constant

  # Rationale: Provides consistency in coding style and follows modern practices of the language
  - legacy_constructor

  # Rationale: Provides consistency in coding style and follows modern practices of the language
  - legacy_hashing

  # Rationale: Provides consistency in coding style and follows modern practices of the language
  - legacy_nsgeometry_functions

  # Rationale: Usage of proper access level
  - lower_acl_than_parent

  # Rationale: Provides consistency in coding style
  - mark

  # Rationale: Provides consistency in coding style
  - multiline_parameters

  # Rationale: Clarity of code
  - multiple_closures_with_trailing_closure

  # Rationale: Provides consistency in coding style
  - no_space_in_method_call

  # Rationale: Encourages coder best practices though language feature likely makes this obsolete
  - notification_center_detachment

  # Rationale: Provides consistency in coding style
  - opening_brace

  # Rationale: Provides consistency in coding style
  - operator_usage_whitespace

  # Rationale: Provides consistency in coding style
  - operator_whitespace

  # Rationale: Prevents coder error
  - overridden_super_call

  # Rationale: Prevents unpredictable behavior
  - override_in_extension

  # Rationale: Promotes consistency and reduces duplication.
  - pattern_matching_keywords

  # Rationale: Keep internal details from being overexposed
  - private_over_fileprivate

  # Rationale: Prevents coder error
  - private_unit_test

  # Rationale: Prevents coder error
  - prohibited_super_call

  # Rationale: Provides consistency in coding style
  - protocol_property_accessors_order

  # Rationale: Provides consistency in coding style and brevity
  - redundant_discardable_let

  # Rationale: Provides consistency in coding style and brevity
  - redundant_nil_coalescing

  # Rationale: Provides consistency in coding style and brevity
  - redundant_objc_attribute

  # Rationale: Provides consistency in coding style and brevity
  - redundant_optional_initialization

  # Rationale: Provides consistency in coding style and brevity
  - redundant_void_return

  # Rationale: Provides consistency in coding style
  - required_enum_case

  # Rationale: Provides consistency in coding style
  - return_arrow_whitespace

  # Rationale: Provides consistency in coding style
  - shorthand_operator

  # Rationale: There should be only XCTestCase per file
  - single_test_class

  # Rationale: Provides consistency and clarity in coding style and is less code
  - sorted_first_last

  # Rationale: Provides consistency in coding style
  - statement_position

  # Rationale: Provides cleaniness of code
  - superfluous_disable_command

  # Rationale: Provides consistency in coding style
  - switch_case_alignment

  # Rationale: Provides consistency in coding style and follows modern practices of the language
  - syntactic_sugar

  # Rationale: Provides consistency in coding style
  - trailing_newline

  # Rationale: Provides consistency in coding style and follows modern practices of the language
  - trailing_semicolon

  # Rationale: Provides consistency in coding style and brevity
  - unneeded_break_in_switch

  # Rationale: Provides consistency in coding style and brevity
  - unused_control_flow_label

  # Rationale: Provides consistency in coding style and brevity
  - unused_closure_parameter

  # Rationale: Provides consistency in coding style and brevity
  - unused_enumerated

  # Rationale: Provides consistency in coding style and brevity
  - unused_optional_binding

  # Rationale: Avoids issues where the setter is not using the value passed in.
  - unused_setter_value

  # Rationale: Prevents coder error
  - valid_ibinspectable

  # Rationale: Provides consistency in coding style
  - vertical_parameter_alignment

  # Rationale: Provides consistency in coding style
  - vertical_parameter_alignment_on_call

  # Rationale: Provides consistency in coding style
  - vertical_whitespace

  # Rationale: Provides consistency in coding style
  - vertical_whitespace_closing_braces

  # Rationale: Provides consistency in coding style and follows modern practices of the language
  - void_return

  # Rationale: Prevents retain cycles and coder error
  - weak_delegate

  # Rationale: Encourages better documentation
  - xctfail_message

  # Rationale: Provides consistency in coding style
  - yoda_condition

  # Rationale: Provides consistency in coding style.
  - reduce_boolean

  # Rationale: == is not used for NSObject comparison, and could lead to confusion.
  - nsobject_prefer_isequal

  # Rationale: Prevents issues with using unowned.
  - unowned_variable_capture

  # Rationale: Ensures all enums can be switched upon.
  - duplicate_enum_cases

  # Rationale: Provides consistency in coding style.
  - legacy_multiple

  # Closure end should have the same indentation as the line that started it.
  - closure_end_indentation

  # All elements in a collection literal should be vertically aligned
  - collection_alignment

  # Prefer // comment over //comment
  - comment_spacing

  # Refer assertionFailure() over assert(false)
  - discouraged_assert

  # Discourage enum cases that interfere with Optional<T>.none and type checking.
  - discouraged_none_name

  # Encourages initializers over object literals
  - discouraged_object_literal

  # Empty XCTest methods should be avoided.
  - empty_xctest_method

  # Swift files must have our copyright header info.
  - file_header

  # Comparing two identical operands is likely a mistake.
  - identical_operands

  # Customized identifier name rules
  - identifier_name

  # Let and var should be separated from other statements by a blank line.
  - let_var_whitespace

  # Array and dictionary literal end should have the same indentation as the line that started it.
  - literal_expression_end_indentation

  # Modifier order should be consistent
  - modifier_order

  # Static strings should be used as a key/comment in NSLocalizedString in order for genstrings to work.
  - nslocalizedstring_key

  # Matching an enum case against an optional enum without '?' is supported on Swift 5.1 and above.
  - optional_enum_case_matching

  # Using Self to reference a Type when possible is more stable
  - prefer_self_in_static_references

  # Prefer `Self` over `type(of: self)` when accessing properties or calling methods.
  - prefer_self_type_over_type_of_self

  # Prefer `.zero` over explicit init with zero parameters (e.g., `CGPoint(x: 0, y: 0)`)
  - prefer_zero_over_explicit_init

  # Mutable reference can be faster than repeated copying
  - reduce_into

  # Catch uses of self inside an inline closure used for initializing a variable
  - self_in_property_initialization

  # Operators should be declared as static functions, not free functions.
  - static_operator

  # Test case API should be private for that test case
  - test_case_accessibility

  # Prefer `someBool.toggle()` over `someBool = !someBool`
  - toggle_bool

  # Parentheses are not needed when declaring closure arguments.
  - unneeded_parentheses_in_closure_argument

  # Test classes must implement balanced setUp and tearDown methods
  - balanced_xctest_lifecycle

  # Types used for hosting only static members should be implemeted as a caseless enum to avoid instantiation.
  - convenience_type

  # Number of associated values in an enum should be low
  - enum_case_associated_values_count

  # Arguments should be either on the same line, or one per line
  - multiline_arguments

  # Multiline arguments should have their surrounding brackets in a new line.
  - multiline_arguments_brackets

  # Chained function calls should be either on the same line, or one per line.
  - multiline_function_chains

  # Multiline literals should have their surrounding brackets in a new line.
  - multiline_literal_brackets

  # Multiline parameters should have their surrounding brackets in a new line.
  - multiline_parameters_brackets

  # Trailing closure syntax should be used whenever possible.
  - trailing_closure

  # Uncallable or unreachabable implimentations should be marked unavailable
  - unavailable_function

  # Catch statements should not declare error variables without type casting.
  - untyped_error_in_catch

  # Prefer specific XCTest matchers over `XCTAssertEqual` and `XCTAssertNotEqual`
  - xct_specific_matcher

attributes:
  always_on_same_line: ["@IBAction", "@IBSegueAction", "@NSManaged", "@escaping", "@objc", "@frozen"]

file_length:
  warning: 1000
  error: 2000

function_body_length:
  warning: 200
  error: 300

identifier_name:
  min_length: 1
  max_length:
    warning: 60
    error: 80
  allowed_symbols: ["_"]
  validates_start_with_lowercase: error

inclusive_language:
  override_allowed_terms: ["master", "blacklist", "whitelist"]

large_tuple:
  warning: 3
  error: 3

line_length:
  warning: 400
  error: 400

nesting:
  type_level: 2
  function_level: 5

trailing_comma:
  mandatory_comma: true

type_name:
  max_length:
    warning: 75
    error: 75
