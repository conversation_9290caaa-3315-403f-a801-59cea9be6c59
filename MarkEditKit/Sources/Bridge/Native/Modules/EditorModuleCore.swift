//
//  EditorModuleCore.swift
//
//  Created by cyan on 12/24/22.
//

import Foundation

@MainActor
public protocol EditorModuleCoreDelegate: AnyObject {
  func editorCoreWindowDidLoad(_ sender: EditorModule<PERSON>ore)
  func editorCoreEditorDidBecomeIdle(_ sender: EditorModuleCore)
  func editorCoreBackgroundColorDidChange(_ sender: Editor<PERSON><PERSON>ule<PERSON><PERSON>, color: UInt32, alpha: Double)
  func editorCoreViewportScaleDidChange(_ sender: EditorModuleCore)
  func editorCoreViewDidUpdate(
    _ sender: EditorModuleCore,
    contentEdited: Bool,
    compositionEnded: Bool,
    isDirty: Bool,
    selectedLineColumn: LineColumnInfo
  )
  func editorCoreContentHeightDidChange(_ sender: EditorModuleCore, bottomPanelHeight: Double)
  func editorCoreContentOffsetDidChange(_ sender: EditorModule<PERSON><PERSON>)
  func editorCoreCompositionEnded(_ sender: EditorM<PERSON>ule<PERSON><PERSON>, selectedLineColumn: LineColumnInfo)
  func editorCoreLinkClicked(_ sender: EditorModule<PERSON>ore, link: String)
  func editorCoreLightWarning(_ sender: EditorModuleCore)
}

public final class EditorModuleCore: NativeModuleCore {
  private weak var delegate: EditorModuleCoreDelegate?

  public init(delegate: EditorModuleCoreDelegate) {
    self.delegate = delegate
  }

  public func notifyWindowDidLoad() {
    delegate?.editorCoreWindowDidLoad(self)
  }

  public func notifyEditorDidBecomeIdle() {
    delegate?.editorCoreEditorDidBecomeIdle(self)
  }

  public func notifyBackgroundColorDidChange(color: Int, alpha: Double) {
    delegate?.editorCoreBackgroundColorDidChange(self, color: UInt32(color), alpha: alpha)
  }

  public func notifyViewportScaleDidChange() {
    delegate?.editorCoreViewportScaleDidChange(self)
  }

  public func notifyViewDidUpdate(
    contentEdited: Bool,
    compositionEnded: Bool,
    isDirty: Bool,
    selectedLineColumn: LineColumnInfo
  ) {
    delegate?.editorCoreViewDidUpdate(
      self,
      contentEdited: contentEdited,
      compositionEnded: compositionEnded,
      isDirty: isDirty,
      selectedLineColumn: selectedLineColumn
    )
  }

  public func notifyContentHeightDidChange(bottomPanelHeight: Double) {
    delegate?.editorCoreContentHeightDidChange(self, bottomPanelHeight: bottomPanelHeight)
  }

  public func notifyContentOffsetDidChange() {
    delegate?.editorCoreContentOffsetDidChange(self)
  }

  public func notifyCompositionEnded(selectedLineColumn: LineColumnInfo) {
    delegate?.editorCoreCompositionEnded(self, selectedLineColumn: selectedLineColumn)
  }

  public func notifyLinkClicked(link: String) {
    delegate?.editorCoreLinkClicked(self, link: link)
  }

  public func notifyLightWarning() {
    delegate?.editorCoreLightWarning(self)
  }
}
