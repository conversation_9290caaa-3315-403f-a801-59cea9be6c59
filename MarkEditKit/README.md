# MarkEditKit

This package provides most functionalities to use the CoreEditor, including a WKWebView wrapper, and bi-directional communication between web application and native code.

Most code in this package is automatically generated by inferring the TypeScript code, which is located in the CoreEditor folder. To understand how it works, take a look at [ts-gyb](https://github.com/microsoft/ts-gyb).

> Ideally, this package should be able to run on both macOS and iOS.