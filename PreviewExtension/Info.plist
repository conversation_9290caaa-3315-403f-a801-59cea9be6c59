<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSExtension</key>
	<dict>
		<key>NSExtensionAttributes</key>
		<dict>
			<key>QLIsDataBasedPreview</key>
			<false/>
			<key>QLSupportedContentTypes</key>
			<array>
				<string>net.daringfireball.markdown</string>
				<string>org.textbundle.package</string>
				<string>net.ia.markdown</string>
				<string>app.markedit.md</string>
				<string>app.markedit.markdown</string>
				<string>app.markedit.txt</string>
				<string>public.markdown</string>
				<string>org.quarto.qmarkdown</string>
				<string>com.unknown.md</string>
				<string>com.rstudio.rmarkdown</string>
				<string>com.nutstore.down</string>
				<string>dyn.ah62d4rv4ge81e5pe</string>
				<string>dyn.ah62d4rv4ge8043a</string>
				<string>dyn.ah62d4rv4ge81c5pe</string>
			</array>
			<key>QLSupportsSearchableItems</key>
			<false/>
		</dict>
		<key>NSExtensionPointIdentifier</key>
		<string>com.apple.quicklook.preview</string>
		<key>NSExtensionPrincipalClass</key>
		<string>$(PRODUCT_MODULE_NAME).PreviewViewController</string>
	</dict>
</dict>
</plist>
