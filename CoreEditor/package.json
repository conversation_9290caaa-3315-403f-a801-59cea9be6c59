{"name": "markedit-app", "version": "1.0.0", "description": "Just like TextEdit on Mac but dedicated to <PERSON><PERSON>.", "scripts": {"dev": "vite", "lint": "eslint .", "codegen": "ts-gyb --config ./src/@codegen/config.json", "test": "jest", "build": "yarn lint && yarn codegen && vite build && vite build -c src/@light/vite.config.mts"}, "keywords": [], "author": "", "license": "MIT", "dependencies": {"@codemirror/autocomplete": "^6.0.0", "@codemirror/commands": "^6.0.0", "@codemirror/language": "^6.0.0", "@codemirror/language-data": "^6.0.0", "@codemirror/search": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "@lezer/markdown": "^1.0.0", "js-yaml": "^4.1.0", "uuid": "^10.0.0"}, "devDependencies": {"@types/eslint": "^8.56.10", "@types/jest": "^29.5.4", "@types/js-yaml": "^4.0.5", "@types/uuid": "^10.0.0", "@typescript-eslint/parser": "^7.16.1", "esbuild": "^0.25.0", "eslint": "^9.7.0", "eslint-plugin-compat": "^6.0.0", "eslint-plugin-promise": "^6.6.0", "jest": "^29.6.4", "jest-environment-jsdom": "^29.6.4", "markedit-api": "https://github.com/MarkEdit-app/MarkEdit-api#v0.10.0", "rollup": "^4.0.0", "ts-gyb": "^0.12.0", "ts-jest": "^29.1.1", "tslib": "^2.6.2", "typescript": "^5.0.0", "typescript-eslint": "^7.16.1", "vite": "^5.4.19", "vite-plugin-singlefile": "^2.0.1"}, "packageManager": "yarn@4.5.1"}