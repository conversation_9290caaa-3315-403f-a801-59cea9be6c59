# MarkEdit-app/lang-markdown

For now, lang-markdown is mostly copied from the [@codemirror/lang-markdown](https://github.com/codemirror/lang-markdown) package, with minimal changes to the `insertNewlineContinueMarkup` command and `findSectionEnd` function.

Check "[MarkEdit]" to see the actual modified behavior.

<!-- NOTE: README.md is generated from src/README.md -->

# @codemirror/lang-markdown [![NPM version](https://img.shields.io/npm/v/@codemirror/lang-markdown.svg)](https://www.npmjs.org/package/@codemirror/lang-markdown)

[ [**WEBSITE**](https://codemirror.net/) | [**ISSUES**](https://github.com/codemirror/dev/issues) | [**FORUM**](https://discuss.codemirror.net/c/next/) | [**CHANGELOG**](https://github.com/codemirror/lang-markdown/blob/main/CHANGELOG.md) ]

This package implements Markdown language support for the
[CodeMirror](https://codemirror.net/) code editor.

The [project page](https://codemirror.net/) has more information, a
number of [examples](https://codemirror.net/examples/) and the
[documentation](https://codemirror.net/docs/).

This code is released under an
[MIT license](https://github.com/codemirror/lang-markdown/tree/main/LICENSE).

We aim to be an inclusive, welcoming community. To make that explicit,
we have a [code of
conduct](http://contributor-covenant.org/version/1/1/0/) that applies
to communication around the project.

## API Reference

@markdown

@markdownLanguage

@commonmarkLanguage

@insertNewlineContinueMarkup

@deleteMarkupBackward

@markdownKeymap
