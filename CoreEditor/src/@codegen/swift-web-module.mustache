//
//  {{moduleName}}.swift
//
//  Generated using https://github.com/microsoft/ts-gyb
//
//  Don't modify this file manually, it's auto generated.
//
//  To make changes, edit template files under /CoreEditor/src/@codegen

import WebKit
import Mark<PERSON><PERSON><PERSON><PERSON>

@MainActor
public final class {{moduleName}} {
  private weak var webView: WKWebView?

  init(webView: WKWebView) {
    self.webView = webView
  }
  {{#methods}}

  {{#documentationLines}}
  ///{{{.}}}
  {{/documentationLines}}
  public func {{methodName}}({{parametersDeclaration}}{{#parametersDeclaration.length}}{{^returnType}}, {{/returnType}}{{/parametersDeclaration.length}}{{^returnType}}completion: ((Result<Void, WKWebView.InvokeError>) -> Void)? = nil{{/returnType}}) {{#returnType}}async throws -> {{returnType}} {{/returnType}}{
    {{#parameters.length}}
    struct Message: Encodable {
      {{#parameters}}
      let {{name}}: {{type}}
      {{/parameters}}
    }

    {{/parameters.length}}
    {{#parameters.length}}
    let message = Message(
      {{#parameters}}
      {{name}}: {{name}}{{^last}},{{/last}}
      {{/parameters}}
    )

    {{/parameters.length}}
    {{#returnType}}
    return try await withCheckedThrowingContinuation { continuation in
      webView?.invoke(path: "webModules.{{customTags.invokePath}}.{{methodName}}"{{#parameters.length}}, message: message{{/parameters.length}}) { result in
        Task { @MainActor in
          continuation.resume(with: result)
        }
      }
    }
    {{/returnType}}
    {{^returnType}}
    webView?.invoke(path: "webModules.{{customTags.invokePath}}.{{methodName}}", {{#parameters.length}}message: message, {{/parameters.length}}completion: completion)
    {{/returnType}}
  }
  {{/methods}}
}
{{#associatedTypes}}

{{> swift-named-type}}
{{/associatedTypes}}
