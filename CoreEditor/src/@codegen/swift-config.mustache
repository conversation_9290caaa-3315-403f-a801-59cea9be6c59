//
//  {{moduleName}}.swift
//
//  Generated using https://github.com/microsoft/ts-gyb
//
//  Don't modify this file manually, it's auto generated.
//
//  To make changes, edit template files under /CoreEditor/src/@codegen

import Foundation

public struct {{moduleName}}: Encodable {
  {{#members}}
  {{#documentationLines}}
  ///{{{.}}}
  {{/documentationLines}}
  let {{name}}: {{type}}
  {{/members}}

  public init(
    {{#members}}
    {{name}}: {{type}}{{^last}},{{/last}}
    {{/members}}
  ) {
    {{#members}}
    self.{{name}} = {{name}}
    {{/members}}
  }
}
