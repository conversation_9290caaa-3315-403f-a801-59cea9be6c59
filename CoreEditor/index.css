/* General */

:root {
  color-scheme: light dark;
}

html, body {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* CodeMirror */

.cm-editor {
  height: 100vh;

  /* selectionMatch doesn't work well with some fonts, such as system-ui, ui-rounded */
  font-kerning: none;
}

.cm-gutters {
  cursor: default;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;

  /* To work around a WebKit bug where :hover is not cleared when mouse is outside the window */
  margin: 1px;
}

.cm-lineNumbers {
  /* Disable this to have better experience of "swipe to select multiple lines" */
  pointer-events: none;
}

.cm-focused {
  /* Disable the dashed border when the editor is focused */
  outline: none !important;
}

/* Markdown */

.cm-md-header {
  font-weight: bolder;
}

.cm-md-contentIndent {
  display: inline-block;
}

.cm-md-contentIndent .cm-visibleSpace::before, .cm-md-contentIndent .cm-visibleLineBreak {
  text-indent: 0px;
  margin-inline-start: 0px;
}
